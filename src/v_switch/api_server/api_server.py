"""
API server for v-switch using FastAPI.
"""

import logging
import threading
import async<PERSON>
from typing import Dict, Any, Optional
import uvicorn
from fastapi import FastAPI, HTTPException, Query, Path as PathParam
from pydantic import BaseModel

from v_switch.common.models import ServerMetadata
from v_switch.config.server_config import ServerConfig
from v_switch.core_service.core_service import CoreService


class TestRequest(BaseModel):
    """Request model for test endpoint."""
    tenant_id: str
    test_id: int


class TestDeleteRequest(BaseModel):
    """Request model for deleting test resources."""
    tenant_id: str
    test_id: int

# Pydantic models for request/response data


class CreateSubnetRequest(BaseModel):
    """Request model for creating subnet."""

    tenant_id: str
    vlan_id: int
    subnet_ip_start: Optional[str] = None
    subnet_ip_end: Optional[str] = None
    subnet_ip_mask: Optional[int] = 24
    subnet_gw_ip: str


class QuerySubnetRequest(BaseModel):
    """Request model for querying subnet."""
    tenant_id: str
    vlan_id: int


class DeleteSubnetRequest(BaseModel):
    """Request model for deleting subnet."""
    tenant_id: str
    vlan_id: int


class MountEIPRequest(BaseModel):
    """Request model for mounting EIP."""
    tenant_id: str
    vlan_id: int
    eip: str
    internal_ip: str
    eip_gateway: Optional[str] = None
    rate: Optional[str] = None
    ceil: Optional[str] = None


class ModifyEIPRequest(BaseModel):
    """Request model for mounting EIP."""
    tenant_id: str
    vlan_id: int
    eip: str
    rate: str
    ceil: str


class UnmountEIPRequest(BaseModel):
    """Request model for unmounting EIP."""
    tenant_id: str
    vlan_id: int
    eip: str
    internal_ip: str

class CreateEIPSNATRequest(BaseModel):
    """Request model for creating EIP SNAT."""
    tenant_id: str
    vlan_id: int
    eip: str
    gateway_ip: str
    mask: int = 24

class DeleteEIPSNATRequest(BaseModel):
    """Request model for deleting EIP SNAT."""
    tenant_id: str
    vlan_id: int
    eip: str
    gateway_ip: str
    mask: int = 24

class SuccessResponse(BaseModel):
    """Success response model."""
    success: bool = True
    instruction_id: Optional[str] = None
    message: Optional[str] = None
    data: Optional[Dict[str, Any]] = None


class ErrorResponse(BaseModel):
    """Error response model."""
    error: bool = True
    message: str
    status_code: int


class HealthResponse(BaseModel):
    """Health check response model."""
    success: bool = True
    status: str = "healthy"
    running: bool


class StatusResponse(BaseModel):
    """Server status response model."""
    success: bool = True
    running: bool
    metadata: Optional[ServerMetadata] = None


class InstructionResponse(BaseModel):
    """Instruction status response model."""
    success: bool = True
    instruction: Optional[Dict[str, Any]] = None


def create_fastapi_app(core_service: CoreService) -> FastAPI:
    """Create FastAPI application with all routes.

    Args:
        core_service: Core service instance

    Returns:
        FastAPI application instance
    """
    app = FastAPI(
        title="V-Switch API",
        description="API server for v-switch network management",
        version="1.0.0"
    )

    logger = logging.getLogger(__name__)

    # 健康检查
    @app.get("/health", response_model=HealthResponse)
    async def health_check():
        """健康检查"""
        try:
            return HealthResponse(
                success=True,
                status="healthy",
                running=core_service.is_running()
            )
        except Exception as e:
            logger.error(f"Error in health check: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {e}")
        
    # 心跳
    @app.get("/heartbeat", response_model=SuccessResponse)
    async def heartbeat():
        """心跳"""
        return SuccessResponse(success=True)

    # 服务状态
    @app.get("/status", response_model=StatusResponse)
    async def server_status():
        """获取服务器状态和元数据。"""
        try:
            metadata = core_service.get_server_metadata()
            return StatusResponse(
                success=True,
                running=core_service.is_running(),
                metadata=metadata
            )
        except Exception as e:
            logger.error(f"Error getting server status: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {e}")

    # 创建子网
    @app.post("/network/subnet", response_model=SuccessResponse)
    async def create_subnet(request: CreateSubnetRequest):
        """创建子网"""
        try:
            logger.info(f"Creating subnet for {request}")

            subnet_id = core_service.create_subnet(
                tenant_id=request.tenant_id,
                vlan_id=request.vlan_id,
                subnet_gw_ip=request.subnet_gw_ip,
                subnet_ip_start=request.subnet_ip_start,
                subnet_ip_end=request.subnet_ip_end,
                subnet_ip_mask=request.subnet_ip_mask
            )

            if subnet_id:
                return SuccessResponse(
                    success=True,
                    instruction_id=subnet_id,
                    message="Subnet created successfully"
                )
            else:
                raise HTTPException(status_code=500, detail="Failed to create subnet")

        except ValueError as e:
            logger.error(f"Validation error creating subnet: {e}")
            raise HTTPException(status_code=400, detail=str(e))
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating subnet: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {e}")

    # 查询子网
    @app.get("/network/subnet", response_model=SuccessResponse)
    async def query_subnet(tenant_id: str, vlan_id: int):
        """查询子网"""
        try:
            logger.info(f"Querying subnet for tenant {tenant_id}, VLAN vlan-{vlan_id}")

            subnet_config = core_service.query_subnet(
                tenant_id=tenant_id,
                vlan_id=vlan_id
            )

            if subnet_config:
                return SuccessResponse(
                    success=True,
                    data=subnet_config,
                    message="Subnet found"
                )
            else:
                raise HTTPException(status_code=404, detail="Subnet not found")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error querying subnet: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {e}")

    # 修改子网
    @app.put("/network/subnet", response_model=SuccessResponse)
    async def modify_subnet(request: CreateSubnetRequest):
        """修改子网"""
        try:
            logger.info(f"Modifying subnet for {request}")

            subnet_id = core_service.modify_subnet(
                tenant_id=request.tenant_id,
                vlan_id=request.vlan_id,
                subnet_gw_ip=request.subnet_gw_ip,
                subnet_ip_start=request.subnet_ip_start,
                subnet_ip_end=request.subnet_ip_end,
                subnet_ip_mask=request.subnet_ip_mask
            )

            if subnet_id:
                return SuccessResponse(
                    success=True,
                    instruction_id=subnet_id,
                    message="Subnet created successfully"
                )
            else:
                raise HTTPException(status_code=500, detail="Failed to create subnet")

        except ValueError as e:
            logger.error(f"Validation error creating subnet: {e}")
            raise HTTPException(status_code=400, detail=str(e))
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating subnet: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {e}")

    # 删除子网
    @app.delete("/network/subnet", response_model=SuccessResponse)
    async def delete_subnet(request: DeleteSubnetRequest):
        """删除子网"""
        try:
            logger.info(f"Deleting subnet for {request}")

            success = core_service.delete_subnet(
                request.tenant_id,
                request.vlan_id
            )

            if success:
                return SuccessResponse(
                    success=True,
                    message="Subnet deleted successfully"
                )
            else:
                raise HTTPException(status_code=500, detail="Failed to delete subnet")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting subnet: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {e}")

    # 挂载EIP
    @app.post("/network/eip/mount", response_model=SuccessResponse)
    async def mount_eip(request: MountEIPRequest):
        """挂载EIP"""
        try:
            logger.info(f"Mounting EIP Params: {request}")
            success = core_service.mount_eip(
                request.tenant_id,
                request.vlan_id,
                request.eip,
                request.internal_ip,
                request.eip_gateway,
                request.rate,
                request.ceil
            )

            if success:
                return SuccessResponse(
                    success=True,
                    message="EIP mounted successfully"
                )
            else:
                raise HTTPException(status_code=500, detail="Failed to mount EIP")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error mounting EIP: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {e}")
    
    # 修改EIP限速
    @app.put("/network/eip/mount", response_model=SuccessResponse)
    async def modify_eip(request: ModifyEIPRequest):
        """修改EIP限速"""
        try:
            logger.info(f"Modifying EIP Params: {request}")
            success = core_service.modify_eip(
                request.tenant_id,
                request.vlan_id,
                request.eip,
                request.rate,
                request.ceil
            )

            if success:
                return SuccessResponse(
                    success=True,
                    message="EIP modified successfully"
                )
            else:
                raise HTTPException(status_code=500, detail="Failed to modify EIP")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error modifying EIP: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {e}")

    # 卸载EIP
    @app.delete("/network/eip/mount", response_model=SuccessResponse)
    async def unmount_eip(request: UnmountEIPRequest):
        """卸载EIP"""
        try:
            logger.info(f"Unmounting EIP Param {request}")

            success = core_service.unmount_eip(
                request.tenant_id,
                request.vlan_id,
                request.eip,
                request.internal_ip
            )

            if success:
                return SuccessResponse(
                    success=True,
                    message="EIP unmounted successfully"
                )
            else:
                raise HTTPException(status_code=500, detail="Failed to unmount EIP")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error unmounting EIP: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {e}")

    # 创建EIP SNAT
    @app.post("/network/eip/snat", response_model=SuccessResponse)
    async def create_eip_snat(request: CreateEIPSNATRequest):
        """创建EIP SNAT"""
        try:
            logger.info(f"Creating EIP SNAT for {request}")
            success = core_service.create_eip_snat(
                request.tenant_id,
                request.vlan_id,
                request.eip,
                request.gateway_ip,
                request.mask
            )

            if success:
                return SuccessResponse(
                    success=True,
                    message="EIP SNAT created successfully"
                )
            else:
                raise HTTPException(status_code=500, detail="Failed to create EIP SNAT")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating EIP SNAT: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {e}")

    # 删除EIP SNAT
    @app.delete("/network/eip/snat", response_model=SuccessResponse)
    async def delete_eip_snat(request: DeleteEIPSNATRequest):
        """删除EIP SNAT"""
        try:
            logger.info(f"Deleting EIP SNAT for {request}")
            success = core_service.delete_eip_snat(
                request.tenant_id,
                request.vlan_id,
                request.eip,
                request.gateway_ip,
                request.mask
            )

            if success: 
                return SuccessResponse(
                    success=True,
                    message="EIP SNAT deleted successfully"
                )
            else:
                raise HTTPException(status_code=500, detail="Failed to delete EIP SNAT")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting EIP SNAT: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {e}")
        
    # 创建EIP DNAT
    class CreateEIPDNATRequest(BaseModel):
        """Request model for creating EIP DNAT."""
        tenant_id: str
        vlan_id: int
        eip: str
        internal_ip: str
        type: str
        dport: int
        port: int
    
    @app.post("/network/eip/dnat", response_model=SuccessResponse)
    async def create_eip_dnat(request: CreateEIPDNATRequest):
        """创建EIP DNAT"""
        try:
            logger.info(f"Creating EIP DNAT for {request}")
            success = core_service.create_eip_dnat(
                request.tenant_id,
                request.vlan_id,
                request.eip,
                request.internal_ip,
                request.type,
                request.dport,
                request.port
            )

            if success:
                return SuccessResponse(
                    success=True,
                    message="EIP DNAT created successfully"
                )
            else:
                raise HTTPException(status_code=500, detail="Failed to create EIP DNAT")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating EIP DNAT: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {e}")
    
    class DeleteEIPDNATRequest(BaseModel):
        """Request model for deleting EIP DNAT."""
        tenant_id: str
        vlan_id: int
        eip: str
        internal_ip: str
        type: str
        dport: int
        port: int

    @app.delete("/network/eip/dnat", response_model=SuccessResponse)
    async def delete_eip_dnat(request: DeleteEIPDNATRequest):
        """删除EIP DNAT"""
        try:
            logger.info(f"Deleting EIP DNAT for {request}")
            success = core_service.delete_eip_dnat(
                request.tenant_id,
                request.vlan_id,
                request.eip,
                request.internal_ip,
                request.type,
                request.dport,
                request.port
            )

            if success:
                return SuccessResponse(
                    success=True,
                    message="EIP DNAT deleted successfully"
                )
            else:
                raise HTTPException(status_code=500, detail="Failed to delete EIP DNAT")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting EIP DNAT: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {e}")


    return app


class APIServer:
    """Main API server class using FastAPI."""

    def __init__(self, config: ServerConfig, core_service: CoreService):
        """Initialize API server.

        Args:
            config: Server configuration
            core_service: Core service instance
        """
        self.config = config
        self.core_service = core_service
        self.logger = logging.getLogger(__name__)

        # Create FastAPI app
        self.app = create_fastapi_app(core_service)

        # Server state
        self.server = None
        self.server_thread = None
        self._running = False

    def start(self) -> bool:
        """Start the API server.

        Returns:
            True if successful
        """
        try:
            if self._running:
                self.logger.warning("API server is already running")
                return True

            # Configure uvicorn
            config = uvicorn.Config(
                app=self.app,
                host="0.0.0.0",
                port=self.config.server.port,
                log_level="info",
                access_log=True
            )

            self.server = uvicorn.Server(config)

            # Start server in background thread
            self.server_thread = threading.Thread(
                target=self._run_server,
                name="APIServer",
                daemon=True
            )

            self._running = True
            self.server_thread.start()

            self.logger.info(f"FastAPI server started on port {self.config.server.port}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to start API server: {e}")
            return False

    def stop(self) -> None:
        """Stop the API server."""
        try:
            if not self._running:
                return

            self.logger.info("Stopping API server...")
            self._running = False

            # Shutdown uvicorn server
            if self.server:
                self.server.should_exit = True

            # Wait for server thread to finish
            if self.server_thread and self.server_thread.is_alive():
                self.server_thread.join(timeout=5.0)

            self.logger.info("API server stopped")

        except Exception as e:
            self.logger.error(f"Error stopping API server: {e}")

    def _run_server(self) -> None:
        """Run the uvicorn server."""
        try:
            self.logger.info("Starting FastAPI server loop")
            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.server.serve())
        except Exception as e:
            if self._running:  # Only log if we're supposed to be running
                self.logger.error(f"FastAPI server error: {e}")

    def is_running(self) -> bool:
        """Check if API server is running.

        Returns:
            True if running
        """
        return self._running
