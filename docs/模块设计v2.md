# cloudlink SDN 模块设计v2
## core 
启动时连接到MariaDB数据库,

### 表设计
endpoint 表, 记录所有端点信息
```
id: 主键
name: 端点名称
group_id: 端点分组标识
status: 端点状态, online | offline
last_heartbeat_time: 最后一次心跳时间
```

ip_pool 表, 记录所有ip池信息
```
id: 主键
name: ip池名称
type: ip池类型, monnet | eipnet
description: 描述
ip_start: ip池起始ip
ip_end: ip池结束ip
vlan_id: 该ip池所属的vlan id
next_ip: 下一个分配的ip
```

ip_pool_allocated 表, 记录ip池已分配ip信息
```
id: 主键
pool_id: 关联的ip池id
pool_type: 关联的ip池类型, monnet | eipnet
ip: 分配的ip
status: 分配状态, allocated | reclaimed
```


subnet 表, 记录所有子网信息
```
id: 主键
tenant_id: 租户标识
vlan_id: 子网vlan id
ip_start: 子网起始ip
ip_end: 子网结束ip
ip_mask: 子网掩码
gateway_ip: 子网网关ip
```

monnet 表, 记录所有监控网络信息
```
id: 主键
subnet_id: 子网id
mon_gateway_ip: 监控网关ip
mon_ip: 监控ip
mon_m_ip: 监控管理ip
mon_m_ports: 监控管理端口
ext_m_ports: 监控扩展端口
```

eip 表, 记录所有eip
```
id: 主键
subnet_id: 子网id
eip: eip地址
internal_ip: 内网ip
rate: 保证带宽, 这是该EIP在任何情况下都能获得的最低速率。不填则默认满带宽
ceil: 最高带宽, 当有空闲带宽时，该EIP可以借用带宽，但不能超过这个上限。不填则默认满带宽
```

snat 表, 记录所有snat规则
```
id: 主键
subnet_id: 子网id
eip: eip地址
gateway_ip: 网关ip
mask: 网关掩码
```

dnat 表, 记录所有dnat规则
```
id: 主键
subnet_id: 子网id
eip: eip地址
internal_ip: 内网ip
```
